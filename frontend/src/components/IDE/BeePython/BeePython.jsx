import { Box, CircularProgress, Divider, I<PERSON><PERSON>utton, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import React, { useEffect } from "react";
import BeeAppBar from "./BeeAppBar";
import ProjectDrawer from "./ProjectDrawer";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { grey } from "@mui/material/colors";
import Editor from "@monaco-editor/react";

import { Terminal } from "xterm";
import { FitAddon } from "xterm-addon-fit";
import { WebLinksAddon } from "xterm-addon-web-links";
import "xterm/css/xterm.css";
import { serialConnection } from "../Common/Connection";
import DeleteIcon from "@mui/icons-material/Delete";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import BeePythonChatAssistant from "./BeePythonChatAssistant";
import "./BeePython.css";

import {
    pythonSuggestions,
    pythonClassMethods,
    micropythonSuggestions,
    pythonKeywords,
    micropythonKeywords,
    triggerCharacters,
    getMonacoKind,
} from "../Common/PythonSuggestions";

import { generateSuggestions } from "../Common/BeeModules";

function BeePython({ user, setUser }) {
    useDocumentTitle("MicroPython | BeE");

    const terminalRef = React.useRef(null);
    const terminalDivRef = React.useRef(null);
    const containerRef = React.useRef(null);
    const fitAddon = React.useRef(null);
    const editorRef = React.useRef(null);
    const fileInputRef = React.useRef(null);

    const [terminal, setTerminal] = React.useState(null);
    const [width, setWidth] = React.useState(400);
    const [isDragging, setIsDragging] = React.useState(false);
    const [message, setMessage] = React.useState("");
    const [uploading, setUploading] = React.useState(false);
    const [serialMonitorInterval, setSerialMonitorInterval] = React.useState(null);

    const [drawerOpen, setDrawerOpen] = React.useState(true);

    // Resize terminal function
    const handleMouseDown = (e) => {
        setIsDragging(true);

        const startX = e.clientX;
        const startWidth = width;

        const handleMouseMove = (e) => {
            const newWidth = startWidth - (e.clientX - startX); // Decrease right width when dragging left
            if (newWidth <= 200) {
                setWidth(200);
            } else if (newWidth >= 600) {
                setWidth(600);
            } else {
                setWidth(newWidth); // Prevent shrinking too much
            }
            fitAddon.current.fit();
        };

        const handleMouseUp = () => {
            setIsDragging(false);
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
    };

    // Initialize terminal
    useEffect(() => {
        if (terminalDivRef.current && !terminal) {
            const term = new Terminal({
                cursorBlink: true,
                fontSize: 12,
                fontFamily: 'Menlo, Monaco, "Courier New", monospace',
                theme: {
                    background: "#1e1e1e",
                    foreground: "#ffffff",
                },
            });

            fitAddon.current = new FitAddon();
            term.loadAddon(fitAddon.current);
            term.loadAddon(new WebLinksAddon());

            term.open(terminalDivRef.current);
            fitAddon.current.fit();

            term.writeln("MicroPython Terminal Ready");
            term.writeln("-------------------");

            // Handle terminal input
            term.onData((data) => {
                if (serialConnection.writer) {
                    const encoder = new TextEncoder();
                    serialConnection.writer.write(encoder.encode(data)).catch((err) => {
                        console.error("Error writing to serial:", err);
                    });
                }
            });

            setTerminal(term);
            terminalRef.current = term;

            setMessage("Terminal is initialized");
        }

        return () => {
            if (terminal) {
                terminal.dispose();
            }
        };
    }, [terminalDivRef.current]);

    // Load project and files from localStorage
    const [project, setProject] = React.useState(() => {
        const savedProject = localStorage.getItem("BeePythonProject");
        const defaultProject = {
            name: "My Project",
            serialConnected: false,
            port: null,
            writer: null,
            reader: null,
        };

        if (savedProject) {
            const loadedProject = JSON.parse(savedProject);
            setMessage("Project loaded successfully");
            return {
                ...defaultProject,
                name: loadedProject.name || "My Project",
            };
        }
        return defaultProject;
    });

    const [files, setFiles] = React.useState(() => {
        const savedProject = localStorage.getItem("BeePythonProject");
        const defaultFiles = [
            {
                id: "main",
                name: "main.py",
                content: "",
                isActive: true,
            },
        ];

        if (savedProject) {
            const loadedProject = JSON.parse(savedProject);
            if (loadedProject.files && Array.isArray(loadedProject.files)) {
                return loadedProject.files;
            } else if (loadedProject.pythonCode !== undefined) {
                // Migration from old single-file format
                return [
                    {
                        id: "main",
                        name: "main.py",
                        content: loadedProject.pythonCode || "",
                        isActive: true,
                    },
                ];
            }
        }
        return defaultFiles;
    });

    const [activeFileId, setActiveFileId] = React.useState(() => {
        const savedProject = localStorage.getItem("BeePythonProject");
        if (savedProject) {
            const loadedProject = JSON.parse(savedProject);
            if (loadedProject.activeFileId) {
                return loadedProject.activeFileId;
            }
        }
        return "main";
    });

    // Save project, files, and activeFileId to localStorage
    useEffect(() => {
        localStorage.setItem(
            "BeePythonProject",
            JSON.stringify({
                name: project.name,
                files: files,
                activeFileId: activeFileId,
            })
        );
    }, [project.name, files, activeFileId]);

    // Helper functions for file management
    const getCurrentFile = () => {
        return files.find((file) => file.id === activeFileId) || files[0];
    };

    const handleFileSelect = (fileId) => {
        setActiveFileId(fileId);
    };

    const handleFileAdd = (fileName) => {
        const newFileId = Date.now().toString();
        const newFile = {
            id: newFileId,
            name: fileName,
            content: "",
            isActive: false,
        };
        setFiles((prev) => [...prev, newFile]);
        setActiveFileId(newFileId);
    };

    const handleFileDelete = (fileId) => {
        if (files.length <= 1) return; // Don't delete if only one file

        setFiles((prev) => prev.filter((file) => file.id !== fileId));

        // If deleting active file, switch to first remaining file
        if (fileId === activeFileId) {
            const remainingFiles = files.filter((file) => file.id !== fileId);
            if (remainingFiles.length > 0) {
                setActiveFileId(remainingFiles[0].id);
            }
        }
    };

    const handleFileRename = (fileId, newName) => {
        setFiles((prev) => prev.map((file) => (file.id === fileId ? { ...file, name: newName } : file)));
    };

    const handleFileContentChange = (content) => {
        setFiles((prev) => prev.map((file) => (file.id === activeFileId ? { ...file, content } : file)));
    };

    // Sửa đổi useEffect để theo dõi thay đổi trạng thái kết nối
    useEffect(() => {
        // Nếu kết nối được thiết lập, bắt đầu giám sát
        if (project.serialConnected && project.port) {
            const intervalId = setupSerialDisconnectMonitor();
            setSerialMonitorInterval(intervalId);
        } else {
            // Nếu ngắt kết nối, dừng giám sát
            if (serialMonitorInterval) {
                clearInterval(serialMonitorInterval);
                setSerialMonitorInterval(null);
            }
        }

        // Cleanup khi component unmount
        return () => {
            if (serialMonitorInterval) {
                clearInterval(serialMonitorInterval);
            }
        };
    }, [project.serialConnected, project.port]);

    const handleConnectSerial = async () => {
        const result = await serialConnection.connect();

        if (result.connected && terminalRef.current) {
            // Set up reader loop for terminal
            const readLoop = async () => {
                while (true) {
                    try {
                        const { value, done } = await serialConnection.reader.read();
                        if (done) break;
                        terminalRef.current.write(new TextDecoder().decode(value));
                    } catch (error) {
                        console.error("Error reading from serial:", error);
                        break;
                    }
                }
            };
            readLoop();
        }

        setProject((prev) => ({
            ...prev,
            serialConnected: result.connected,
            port: serialConnection.port,
            writer: serialConnection.writer,
            reader: serialConnection.reader,
        }));

        if (result.message) {
            if (terminalRef.current) {
                terminalRef.current.writeln(result.message);
            }
        }
        setMessage(result.message);
    };

    const handleUploadCode = async () => {
        // Kiểm tra nếu đang upload thì không cho upload nữa
        if (uploading) {
            setMessage("Upload is already in progress...");
            return;
        }

        if (!project.serialConnected) {
            alert("Please connect to the board first!");
            return;
        }

        try {
            setUploading(true);
            handleClearTerminal();
            setMessage("Uploading files...");

            // Separate main.py from other files
            const mainFile = files.find((file) => file.name === "main.py");
            const otherFiles = files.filter((file) => file.name !== "main.py");

            // Upload other files first (without reset)
            for (const file of otherFiles) {
                if (terminalRef.current) {
                    terminalRef.current.writeln(`Uploading ${file.name}...`);
                }

                const result = await serialConnection.uploadFileWithoutReset(file.content, file.name);

                if (result.message) {
                    if (terminalRef.current) {
                        terminalRef.current.writeln(result.message);
                    }
                }

                if (!result.success) {
                    throw new Error(`Failed to upload ${file.name}: ${result.message}`);
                }
            }

            // Upload main.py last (with reset to run the program)
            if (mainFile) {
                if (terminalRef.current) {
                    terminalRef.current.writeln(`Uploading ${mainFile.name}...`);
                }

                const result = await serialConnection.uploadFile(mainFile.content, mainFile.name);

                if (result.message) {
                    if (terminalRef.current) {
                        terminalRef.current.writeln(result.message);
                    }
                }

                if (!result.success) {
                    throw new Error(`Failed to upload ${mainFile.name}: ${result.message}`);
                }
            }

            // Restart the read loop after upload
            if (serialConnection.reader) {
                const readLoop = async () => {
                    while (true) {
                        try {
                            const { value, done } = await serialConnection.reader.read();
                            if (done) break;
                            if (terminalRef.current) {
                                terminalRef.current.write(new TextDecoder().decode(value));
                            }
                        } catch (error) {
                            console.error("Error reading from serial:", error);
                            setMessage("Error: Cannot read from serial");
                            break;
                        }
                    }
                };
                readLoop();
            }

            if (terminalRef.current) {
                terminalRef.current.write(">>> "); // print >>> to terminal to continue to execute code
            }
            setMessage("Done uploading all files");
        } catch (error) {
            console.error("Upload error:", error);
            setMessage("Upload failed: " + error.message);
            if (terminalRef.current) {
                terminalRef.current.writeln("Upload failed: " + error.message);
            }
        } finally {
            setUploading(false);
        }
    };

    // Hàm để monitor serial disconnect
    const setupSerialDisconnectMonitor = () => {
        const checkConnection = async () => {
            try {
                if (!navigator.serial) return;

                const ports = await navigator.serial.getPorts();
                const portExists = ports.some((p) => p === project.port);

                if (!portExists && project.serialConnected) {
                    // Đóng kết nối hiện tại
                    try {
                        // Giải phóng writer nếu đang có
                        if (project.writer) {
                            await project.writer.close();
                        }

                        // Giải phóng reader nếu đang có
                        if (project.reader) {
                            await project.reader.cancel();
                            await project.reader.releaseLock();
                        }

                        // Đóng port
                        if (project.port) {
                            await project.port.close();
                        }
                    } catch (error) {
                        console.warn("Error closing serial connection:", error);
                    }

                    // Cập nhật trạng thái
                    setProject((prev) => ({
                        ...prev,
                        serialConnected: false,
                        port: null,
                        writer: null,
                        reader: null,
                    }));

                    setMessage("Serial disconnected");
                    if (terminalRef.current) {
                        terminalRef.current.writeln("\r\nSerial disconnected");
                    }
                }
            } catch (error) {
                console.error("Error checking port status:", error);
            }
        };

        // Kiểm tra mỗi 2 giây
        const intervalId = setInterval(checkConnection, 2000);
        return intervalId;
    };

    const handleSave = () => {
        // Save current active file
        const currentFile = getCurrentFile();
        if (currentFile) {
            const blob = new Blob([currentFile.content], { type: "text/x-python" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = currentFile.name;

            // Trigger download
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            setMessage(`File ${currentFile.name} saved successfully`);
        }
    };

    const handleLoad = () => {
        fileInputRef.current.click();
    };

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const content = e.target.result;
                const fileName = file.name;

                // Check if file already exists
                const existingFile = files.find((f) => f.name === fileName);
                if (existingFile) {
                    // Update existing file
                    setFiles((prev) => prev.map((f) => (f.name === fileName ? { ...f, content } : f)));
                    setActiveFileId(existingFile.id);
                } else {
                    // Add new file
                    const newFileId = Date.now().toString();
                    const newFile = {
                        id: newFileId,
                        name: fileName,
                        content: content,
                        isActive: false,
                    };
                    setFiles((prev) => [...prev, newFile]);
                    setActiveFileId(newFileId);
                }
            };
            reader.readAsText(file);
            setMessage("File loaded successfully");
        }
    };

    function handleEditorDidMount(editor, monaco) {
        editorRef.current = editor;

        const configurePythonLanguage = (monaco) => {
            // Register Python completions
            monaco.languages.registerCompletionItemProvider("python", {
                provideCompletionItems: (model, position) => {
                    const textUntilPosition = model.getValueInRange({
                        startLineNumber: position.lineNumber,
                        startColumn: 1,
                        endLineNumber: position.lineNumber,
                        endColumn: position.column,
                    });

                    const word = textUntilPosition.trim().split(/\s+/).pop() || "";

                    // Check if we're in a class context
                    const lineText = model.getLineContent(position.lineNumber);
                    const isInClass =
                        /^\s*class\s+/.test(lineText) ||
                        /^\s*def\s+__/.test(lineText) ||
                        textUntilPosition.includes("def __");

                    // Match any "bee." pattern followed by any number of identifiers and dots
                    const beeMatch = textUntilPosition.match(/bee(\.[a-zA-Z_][a-zA-Z0-9_]*)*\.$/);

                    if (beeMatch) {
                        const suggestions = generateSuggestions(beeMatch[0].slice(0, -1));
                        return {
                            suggestions: suggestions.map((item) => ({
                                ...item,
                                kind: getMonacoKind(monaco, item.kind),
                                insertTextRules: item.insertTextRules
                                    ? monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet
                                    : undefined,
                                // Add documentation details
                                detail: item.kind,
                                documentation: {
                                    value: `${item.documentation || ""}`,
                                    isTrusted: true,
                                    supportThemeIcons: true,
                                    supportHtml: true,
                                },
                            })),
                        };
                    }

                    // Combine and convert suggestions
                    let suggestions = [...pythonSuggestions, ...micropythonSuggestions];
                    if (isInClass) {
                        suggestions = [...suggestions, ...pythonClassMethods];
                    }

                    // Filter and convert suggestions
                    const filteredSuggestions = suggestions
                        .filter((s) => s.label.toLowerCase().startsWith(word.toLowerCase()))
                        .map((suggestion) => ({
                            ...suggestion,
                            kind: getMonacoKind(monaco, suggestion.kind),
                            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                            // Add documentation details
                            detail: suggestion.kind,
                            documentation: {
                                value: `${suggestion.documentation || ""}`,
                                supportThemeIcons: true,
                                isTrusted: true,
                                supportHtml: true,
                            },
                            range: {
                                startLineNumber: position.lineNumber,
                                endLineNumber: position.lineNumber,
                                startColumn: position.column - word.length,
                                endColumn: position.column,
                            },
                        }));

                    return { suggestions: filteredSuggestions };
                },
                triggerCharacters,
            });

            // Set up syntax highlighting
            monaco.languages.setMonarchTokensProvider("python", {
                keywords: [...pythonKeywords, ...micropythonKeywords],
                tokenizer: {
                    root: [
                        [
                            /[a-zA-Z_]\w*/,
                            {
                                cases: {
                                    "@keywords": "keyword",
                                    "@default": "identifier",
                                },
                            },
                        ],
                        // ... rest of your tokenizer configuration
                    ],
                },
            });
        };

        configurePythonLanguage(monaco);
    }

    const handleClearTerminal = () => {
        if (terminalRef.current) {
            terminalRef.current.clear();
        }
    };

    return (
        <Box
            sx={{
                height: "100vh",
                display: "flex",
                flexDirection: "column",
                backgroundColor: "white",
                overflow: "hidden",
            }}
        >
            <BeeAppBar
                user={user}
                setUser={setUser}
                project={project}
                setProject={setProject}
                handleConnectSerial={handleConnectSerial}
                handleSave={handleSave}
                handleLoad={handleLoad}
            />
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    flexGrow: 1,
                    mt: "64px",
                    overflow: "hidden",
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        flexGrow: 1,
                        minHeight: 0,
                        flexDirection: { xs: "column", md: "row" },
                        overflowY: "auto",
                    }}
                >
                    {/* Drawer toggle button */}
                    <IconButton
                        onClick={() => setDrawerOpen(!drawerOpen)}
                        sx={{
                            display: drawerOpen ? "none" : "block",
                            position: "fixed",
                            top: "80px",
                            left: drawerOpen ? "260px" : "-20px",
                            zIndex: 1300,
                            backgroundColor: "primary.main",
                            color: "white",
                            width: "40px",
                            height: "40px",
                            transition: "left 0.3s",
                            "&:hover": {
                                backgroundColor: "primary.dark",
                            },
                        }}
                    >
                        <ArrowForwardIcon />
                    </IconButton>

                    {/* Project Drawer */}
                    <ProjectDrawer
                        open={drawerOpen}
                        onClose={() => setDrawerOpen(false)}
                        project={project}
                        onProjectChange={setProject}
                        files={files}
                        activeFileId={activeFileId}
                        onFileSelect={handleFileSelect}
                        onFileAdd={handleFileAdd}
                        onFileDelete={handleFileDelete}
                        onFileRename={handleFileRename}
                    />

                    <Box
                        sx={{
                            flexGrow: 1,
                            flexShrink: 1, // Allow shrinking
                            minWidth: 100, // Prevent it from disappearing completely
                            minHeight: 0,
                            m: 1,
                            ml: drawerOpen ? "290px" : "1px", // Adjust margin based on drawer state
                            transition: "margin-left 0.3s",
                            position: "relative",
                        }}
                    >
                        <Editor
                            height="100%"
                            defaultLanguage="python"
                            theme="vs-light"
                            value={getCurrentFile()?.content || ""}
                            onChange={handleFileContentChange}
                            onMount={handleEditorDidMount}
                            options={{
                                minimap: { enabled: false },
                                fontSize: 14,
                                scrollBeyondLastLine: false,
                                automaticLayout: true,
                                tabSize: 4,
                                insertSpaces: true,
                                suggestOnTriggerCharacters: true,
                                quickSuggestions: true,
                                snippetSuggestions: "inline",
                            }}
                        />
                        <Tooltip title={uploading ? "Uploading..." : "Upload"}>
                            <IconButton
                                onClick={handleUploadCode}
                                disabled={!project.serialConnected || uploading}
                                sx={{
                                    position: "absolute",
                                    top: "40px",
                                    right: "40px",
                                    width: "40px",
                                    height: "40px",
                                    transform: "scale(2)",
                                    boxShadow: "0 0 6px rgb(0 0 0 / 60%)",
                                    backgroundColor: "#212F3C",
                                    zIndex: 100,
                                    "&:hover": {
                                        backgroundColor: "#212F3C",
                                    },
                                    "&.Mui-disabled": {
                                        backgroundColor: "#212F3C",
                                        opacity: 0.1,
                                        cursor: "not-allowed",
                                    },
                                }}
                            >
                                {uploading ? (
                                    <CircularProgress size={20} sx={{ color: "white" }} />
                                ) : (
                                    <FileUploadIcon sx={{ fontSize: "20px", color: "white" }} />
                                )}
                            </IconButton>
                        </Tooltip>
                        {/* <Tooltip title="Upload">
							<IconButton
								onClick={uploadCodeViaOTA}
								// disabled={!project.serialConnected}
								sx={{
									position: "absolute",
									top: "120px",
									right: "30px",
									width: "40px",
									height: "40px",
									transform: "scale(2)",
									boxShadow: "0 0 6px rgb(0 0 0 / 60%)",
									backgroundColor: "#212F3C",
									'&:hover': {
										backgroundColor: "#212F3C"
									},
									'&.Mui-disabled': {
										backgroundColor: '#212F3C',
										opacity: 0.1,
										cursor: "not-allowed"
									}
								}}>
								<FileUploadIcon sx={{ fontSize: "20px", color: "white" }} />
							</IconButton>
						</Tooltip> */}
                    </Box>

                    {/* Resizable divider */}
                    <Box
                        sx={{
                            width: "4px",
                            backgroundColor: isDragging ? "#2196f3" : "#e0e0e0",
                            cursor: "col-resize",
                            "&:hover": {
                                backgroundColor: "#2196f3",
                            },
                            transition: "background-color 0.2s",
                            userSelect: "none", // Prevent text selection while dragging
                        }}
                        onMouseDown={handleMouseDown}
                    />

                    {/* Terminal container with dynamic width */}
                    <Box
                        ref={containerRef}
                        sx={{
                            height: { xs: "40%", md: "100%" },
                            width: { xs: "100%", md: `${width}px` },
                            maxWidth: { xs: "100%", md: "600px" },
                            backgroundColor: "#1e1e1e",
                            borderRadius: 2,
                            overflow: "hidden",
                            display: "flex",
                            flexDirection: "column",
                            transition: isDragging ? "none" : "width 0.2s",
                        }}
                    >
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                p: 1,
                                borderBottom: "1px solid #333",
                                backgroundColor: "#252526",
                            }}
                        >
                            <Typography variant="subtitle2" sx={{ color: "#fff" }}>
                                MicroPython Terminal
                            </Typography>
                            <Box sx={{ display: "flex" }}>
                                <IconButton size="small" onClick={handleClearTerminal}>
                                    <DeleteIcon sx={{ color: "white" }} />
                                </IconButton>
                            </Box>
                        </Box>
                        <Box
                            ref={terminalDivRef}
                            sx={{
                                flexGrow: 1,
                                p: 1,
                                overflow: "auto",
                            }}
                        />
                    </Box>
                </Box>

                <Box
                    sx={{
                        height: "20px",
                        borderTop: "1px solid #ddd",
                        display: "flex",
                        alignItems: "center",
                        justifyItems: "center",
                        px: 2,
                        backgroundColor: "#f5f5f5",
                        ml: drawerOpen ? "280px" : "1px", // Adjust margin based on drawer state
                        transition: "margin-left 0.3s",
                        position: "relative",
                    }}
                >
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Trạng thái:{" "}
                        {project.serialConnected ? (uploading ? "Uploading..." : "Connected") : "Disconnected"}
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Board: BeE board
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Log: {message}
                    </Typography>
                </Box>
            </Box>
            <input
                type="file"
                accept=".py"
                style={{ display: "none" }}
                onChange={handleFileChange}
                ref={fileInputRef}
            />

            {/* BeE Python Chat Assistant */}
            <BeePythonChatAssistant
                files={files}
                activeFileId={activeFileId}
                project={project}
                title="BeE Assistant"
                autoOpen={false}
                persistent={true}
            />
        </Box>
    );
}

export default BeePython;
