import * as Blockly from "blockly";
import { pythonGenerator } from "blockly/python";
import { FieldGridDropdown } from "@blockly/field-grid-dropdown";
import { registerFieldColour } from "@blockly/field-colour";
import "./js/field_note"; // Import the field_note definition

import ButtonIcon from "./images/button.svg";
import GPIOInputIcon from "./images/gpio_input.png";
import ADCInputIcon from "./images/adc_input.png";
import GyroIcon from "./images/gyroscope.png";
import NeoPixelIcon from "./images/neopixel.png";
import OLedIcon from "./images/display.png";
import BuzzerIcon from "./images/buzzer.png";
import WholeNoteIcon from "./images/notes/whole.svg";
import HalfNoteIcon from "./images/notes/half.svg";
import QuarterNoteIcon from "./images/notes/quarter.svg";
import EighthNoteIcon from "./images/notes/eighth.svg";
import SixteenthNoteIcon from "./images/notes/sixteenth.svg";
import MakeNoteIcon from "./images/make-note.svg";
import GPIOOutputIcon from "./images/gpio_output.png";
import WheelIcon from "./images/wheel.png";
import ServoIcon from "./images/rcservo.png";
import MovementIcon from "./images/movement.png";
import OTAIcon from "./images/ota.png";
import UsbIcon from "./images/usb.png";
import ResetIcon from "./images/reset.svg";
import BluetoothIcon from "./images/ble.png";

// Define onboard interrup handler button
Blockly.Blocks["beeboard_onboard_button_on_press_start_program"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_onboard_button_on_press_start_program",
            message0: "%{BKY_BEE_PRESS_BUTTON_START}", //%3 button %1 is %2
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "button",
                    columns: 2,
                    options: [
                        ["A", "buttonA"],
                        ["B", "buttonB"],
                    ],
                },
                {
                    type: "field_dropdown",
                    name: "button_status",
                    options: [
                        ["%{BKY_BEE_BUTTON_PRESSED}", "on_press"],
                        ["%{BKY_BEE_BUTTON_RELEASED}", "on_release"],
                        ["%{BKY_BEE_BUTTON_LONG_PRESSED}", "on_long_press"],
                    ],
                },
                {
                    type: "field_image",
                    src: ButtonIcon,
                    width: 45,
                    height: 45,
                    alt: "start",
                },
            ],
            message1: "%1",
            args1: [
                {
                    type: "input_statement",
                    name: "callback",
                },
            ],
            style: {
                hat: "cap",
            },
            //  "previousStatement": null,
            //  "nextStatement": null,
            colour: "#FFD500",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_onboard_button_on_press_start_program"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var button = block.getFieldValue("button");
    var button_status = block.getFieldValue("button_status");

    var statements_callback = pythonGenerator.statementToCode(block, "callback");
    // -----------------------------
    var globals = [];
    var varName;
    var workspace = block.workspace;
    var variables = Blockly.Variables.allUsedVarModels(workspace) || [];
    for (var i = 0, variable; (variable = variables[i]); i++) {
        varName = variable.name;
        if (block.getVars().indexOf(varName) == -1 && statements_callback.includes(varName)) {
            globals.push(varName);
        }
    }
    // Add developer variables.
    var devVarList = Blockly.Variables.allDeveloperVariables(workspace);
    for (var i = 0; i < devVarList.length; i++) {
        globals.push(pythonGenerator.variableDB_.getName(devVarList[i], Blockly.Names.DEVELOPER_VARIABLE_TYPE));
    }

    globals = globals.length ? pythonGenerator.INDENT + "global " + globals.join(", ") : "";
    statements_callback = statements_callback.length > 0 ? statements_callback : pythonGenerator.INDENT + "pass";
    // -----------------------------
    if (globals != "") {
        var functionName = pythonGenerator.provideFunction_(`${button}_${button_status}_handler`, [
            "def " + pythonGenerator.FUNCTION_NAME_PLACEHOLDER_ + "(pin):",
            globals,
            statements_callback,
        ]);
    } else {
        var functionName = pythonGenerator.provideFunction_(`${button}_${button_status}_handler`, [
            "def " + pythonGenerator.FUNCTION_NAME_PLACEHOLDER_ + "(pin):",
            statements_callback,
        ]);
    }

    pythonGenerator.definitions_[`${button}_irq`] = `bee.${button}.${button_status}(${functionName})`;
    var code = "";
    return code;
};

// Define button status
Blockly.Blocks["beeboard_onboard_button_status"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_onboard_button_status",
            message0: "%{BKY_BEE_BUTTON_STATUS}", //%3 button %1 is %2?
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "button_name",
                    columns: 2,
                    options: [
                        ["A", "buttonA"],
                        ["B", "buttonB"],
                    ],
                },
                {
                    type: "field_dropdown",
                    name: "button_status",
                    options: [
                        ["%{BKY_BEE_BUTTON_PRESSED}", "pressed"],
                        ["%{BKY_BEE_BUTTON_RELEASED}", "released"],
                        ["%{BKY_BEE_BUTTON_LONG_PRESSED}", "long_pressed"],
                    ],
                },
                {
                    type: "field_image",
                    src: ButtonIcon,
                    width: 45,
                    height: 45,
                    alt: "button",
                },
            ],
            inputsInline: true,
            output: "Boolean",
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_onboard_button_status"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var button_name = block.getFieldValue("button_name");
    var button_status = block.getFieldValue("button_status");
    var code = `bee.${button_name}.is_${button_status}()`;

    return [code, pythonGenerator.ORDER_NONE];
};

// Define digital read
Blockly.Blocks["beeboard_gpio_input"] = {
    init: function () {
        this.jsonInit({
            type: "gpio_input",
            message0: "%{BKY_BEE_GPIO_READ}", //%2 %1 digital read
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "port",
                    columns: 2,
                    options: [
                        ["", ""],
                        ["PORT2", "PORT2"],
                        ["PORT3", "PORT3"],
                        ["PORT4", "PORT4"],
                        ["PORT5", "PORT5"],
                        ["PORT6", "PORT6"],
                    ],
                },
                {
                    type: "field_image",
                    src: GPIOInputIcon,
                    width: 45,
                    height: 45,
                    alt: "gpio_input",
                },
            ],
            inputsInline: true,
            output: ["Number", "Boolean"],
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gpio_input"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var port = block.getFieldValue("port");

    if (port === "") {
        alert("Please select a port");
        return;
    }

    var code = `bee.digital_read(bee.${port})`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define analog read
Blockly.Blocks["beeboard_analog_input"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_analog_input",
            message0: "%{BKY_BEE_ANALOG_READ}", //%2 %1 analog read
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "port",
                    columns: 2,
                    options: [
                        ["", ""],
                        ["PORT2", "PORT2"],
                        ["PORT3", "PORT3"],
                        ["PORT4", "PORT4"],
                        ["PORT5", "PORT5"],
                        ["", ""],
                    ],
                },
                {
                    type: "field_image",
                    src: ADCInputIcon,
                    width: 45,
                    height: 45,
                    alt: "gpio_input",
                },
            ],
            inputsInline: true,
            output: "Number",
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_analog_input"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var port = block.getFieldValue("port");
    if (port === "") {
        alert("Please select a port");
        return;
    }
    var code = `bee.analog_read(bee.${port})`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define gyroscope refresh
Blockly.Blocks["beeboard_gyro_calibrate"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gyro_calibrate",
            message0: "%{BKY_BEE_GYRO_CALIBRATE}", //%1 calibrate
            args0: [
                {
                    type: "field_image",
                    src: GyroIcon,
                    width: 45,
                    height: 45,
                    alt: "gyro",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gyro_calibrate"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.imu.calibrate_gyro()\n`;
    return code;
};

// Define gyroscope refresh
Blockly.Blocks["beeboard_gyro_refresh"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gyro_refresh",
            message0: "%{BKY_BEE_GYRO_REFRESH}",
            args0: [
                {
                    type: "field_image",
                    src: GyroIcon,
                    width: 45,
                    height: 45,
                    alt: "gyro",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gyro_refresh"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.imu.update()\n`;
    return code;
};

// Define get roll pitch yaw degree block
Blockly.Blocks["beeboard_gyro_rpy"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gyro_rpy",
            message0: "%{BKY_BEE_GYRO_RPY}", //%2 get %1 (degrees)
            args0: [
                {
                    type: "field_dropdown",
                    name: "angle",
                    options: [
                        ["%{BKY_BEE_GYRO_ROLL}", "rollDeg"],
                        ["%{BKY_BEE_GYRO_PITCH}", "pitchDeg"],
                        ["%{BKY_BEE_GYRO_YAW}", "yawDeg"],
                    ],
                },
                {
                    type: "field_image",
                    src: GyroIcon,
                    width: 45,
                    height: 45,
                    alt: "gyro",
                },
            ],
            inputsInline: true,
            output: "Number",
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gyro_rpy"] = function (block) {
    // Add time import to dependencies if not already added
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    const angle = block.getFieldValue("angle");
    const code = `bee.imu.${angle}`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define is shaking block
Blockly.Blocks["beeboard_gyro_skaking"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gyro_skaking",
            message0: "%{BKY_BEE_GYRO_SKAKING}", //%1 is shaking
            args0: [
                {
                    type: "field_image",
                    src: GyroIcon,
                    width: 45,
                    height: 45,
                    alt: "gyro",
                },
            ],
            inputsInline: true,
            output: ["Boolean"],
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gyro_skaking"] = function (block) {
    // Add time import to dependencies if not already added
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    const code = `bee.imu.is_shaking()`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define gyroscope roll
Blockly.Blocks["beeboard_gyro_roll"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gyro_roll",
            message0: "%1 roll (degrees)",
            args0: [
                {
                    type: "field_image",
                    src: GyroIcon,
                    width: 45,
                    height: 45,
                    alt: "gyroscope",
                },
            ],
            inputsInline: true,
            output: "Number",
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gyro_roll"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.imu.rollDeg`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define gyroscope roll
Blockly.Blocks["beeboard_gyro_pitch"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gyro_pitch",
            message0: "%1 pitch (degrees)",
            args0: [
                {
                    type: "field_image",
                    src: GyroIcon,
                    width: 45,
                    height: 45,
                    alt: "gyroscope",
                },
            ],
            inputsInline: true,
            output: "Number",
            colour: "#9966ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gyro_pitch"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.imu.pitchDeg`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define onboard neopixel rgb
Blockly.Blocks["beeboard_onboard_led_rgb"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_onboard_led_rgb",
            message0: "%{BKY_BEE_LED_RGB}", //%5 %1 on with red %2 green %3 blue %4
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "led_number",
                    options: [
                        ["LED1", "LED1"],
                        ["LED2", "LED2"],
                    ],
                },
                {
                    type: "input_value",
                    name: "red",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "green",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "blue",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: NeoPixelIcon,
                    width: 45,
                    height: 45,
                    alt: "neopixel",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_onboard_led_rgb"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var led_number = block.getFieldValue("led_number");
    var red = pythonGenerator.valueToCode(block, "red", pythonGenerator.ORDER_ATOMIC);
    var green = pythonGenerator.valueToCode(block, "green", pythonGenerator.ORDER_ATOMIC);
    var blue = pythonGenerator.valueToCode(block, "blue", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.neopixel.set_rgb(bee.${led_number}, ${red}, ${green}, ${blue})\n`;

    return code;
};

// Define onboard neopixel picker
Blockly.Blocks["beeboard_onboard_led_on_picker"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_onboard_led_on_picker",
            message0: "%3 %2 on with color %1",
            args0: [
                {
                    type: "field_colour",
                    name: "led_color",
                    // colour: '#ff4040',
                    // colourOptions: [
                    //     '#ff4040',
                    //     '#ff8080',
                    //     '#ffc0c0',
                    //     '#4040ff',
                    //     '#8080ff',
                    //     '#c0c0ff',
                    // ],
                },
                {
                    type: "field_grid_dropdown",
                    name: "led_number",
                    options: [
                        ["LED1", "LED1"],
                        ["LED2", "LED2"],
                    ],
                },
                {
                    type: "field_image",
                    src: NeoPixelIcon,
                    width: 45,
                    height: 45,
                    alt: "neopixel",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_onboard_led_on_picker"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var led_number = block.getFieldValue("led_number");
    var led_color = pythonGenerator.valueToCode(block, "led_color", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.neopixel.in_hex(bee.${led_number}, ${led_color})\n`;
    return code;
};

// Define onboard neopixel off
Blockly.Blocks["beeboard_onboard_led_off"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_onboard_led_off",
            message0: "%{BKY_BEE_LED_OFF}",
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "led_number",
                    options: [
                        ["LED1", "LED1"],
                        ["LED2", "LED2"],
                    ],
                },
                {
                    type: "field_image",
                    src: NeoPixelIcon,
                    width: 45,
                    height: 45,
                    alt: "neopixel",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_onboard_led_off"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";

    var led_number = block.getFieldValue("led_number");
    var code = `bee.neopixel.off(bee.${led_number})\n`;
    return code;
};

// Define onboard oled
Blockly.Blocks["beeboard_clear_oled"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_clear_oled",
            message0: "%{BKY_BEE_OLED_CLEAR}",
            args0: [
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_clear_oled"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.oled.clear()\n`;
    return code;
};

// Define onboard oled
Blockly.Blocks["beeboard_display_oled"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_display_oled",
            message0: "%{BKY_BEE_OLED_DISPLAY}", //%4 display %1 at x: %2 y: %3
            args0: [
                {
                    type: "input_value",
                    name: "message",
                },
                {
                    type: "input_value",
                    name: "x_pos",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y_pos",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_display_oled"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var message = pythonGenerator.valueToCode(block, "message", pythonGenerator.ORDER_ATOMIC);
    var x_pos = pythonGenerator.valueToCode(block, "x_pos", pythonGenerator.ORDER_ATOMIC);
    var y_pos = pythonGenerator.valueToCode(block, "y_pos", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.write(${message}, ${x_pos}, ${y_pos}, 1)\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_draw_pixel"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_draw_pixel",
            message0: "%{BKY_BEE_OLED_DRAW_PIXEL}", //%4 draw pixel at x: %1 y: %2 with color %3
            args0: [
                {
                    type: "input_value",
                    name: "x",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_draw_pixel"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var x = pythonGenerator.valueToCode(block, "x", pythonGenerator.ORDER_ATOMIC);
    var y = pythonGenerator.valueToCode(block, "y", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.draw_pixel(${x}, ${y})\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_draw_line"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_draw_line",
            message0: "%{BKY_BEE_OLED_DRAW_LINE}", //%7 draw line from x: %1 y: %2 to x: %3 y: %4 with color %5 and width %6
            args0: [
                {
                    type: "input_value",
                    name: "x1",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y1",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "x2",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y2",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_draw_line"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var x1 = pythonGenerator.valueToCode(block, "x1", pythonGenerator.ORDER_ATOMIC);
    var y1 = pythonGenerator.valueToCode(block, "y1", pythonGenerator.ORDER_ATOMIC);
    var x2 = pythonGenerator.valueToCode(block, "x2", pythonGenerator.ORDER_ATOMIC);
    var y2 = pythonGenerator.valueToCode(block, "y2", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.draw_line(${x1}, ${y1}, ${x2}, ${y2})\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_draw_rect"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_draw_rect",
            message0: "%{BKY_BEE_OLED_DRAW_RECT}", //%8 draw rectangle from x: %1 y: %2 with width %3 and height %4 with color %5 and border color %6 and border width %7
            args0: [
                {
                    type: "input_value",
                    name: "x",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "width",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "height",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_draw_rect"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var x = pythonGenerator.valueToCode(block, "x", pythonGenerator.ORDER_ATOMIC);
    var y = pythonGenerator.valueToCode(block, "y", pythonGenerator.ORDER_ATOMIC);
    var width = pythonGenerator.valueToCode(block, "width", pythonGenerator.ORDER_ATOMIC);
    var height = pythonGenerator.valueToCode(block, "height", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.draw_rect(${x}, ${y}, ${width}, ${height})\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_draw_circle"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_draw_circle",
            message0: "%{BKY_BEE_OLED_DRAW_CIRCLE}", //%7 draw circle at x: %1 y: %2 with radius %3 with color %4 and border color %5 and border width %6
            args0: [
                {
                    type: "input_value",
                    name: "x",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "radius",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_draw_circle"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var x = pythonGenerator.valueToCode(block, "x", pythonGenerator.ORDER_ATOMIC);
    var y = pythonGenerator.valueToCode(block, "y", pythonGenerator.ORDER_ATOMIC);
    var radius = pythonGenerator.valueToCode(block, "radius", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.draw_circle(${x}, ${y}, ${radius})\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_draw_image"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_draw_image",
            message0: "%{BKY_BEE_OLED_DRAW_IMAGE}", //%3 draw image %1 at x: %2 y: %3
            args0: [
                {
                    type: "input_value",
                    name: "image",
                    check: "String",
                },
                {
                    type: "input_value",
                    name: "x",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_draw_image"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var image = pythonGenerator.valueToCode(block, "image", pythonGenerator.ORDER_ATOMIC);
    var x = pythonGenerator.valueToCode(block, "x", pythonGenerator.ORDER_ATOMIC);
    var y = pythonGenerator.valueToCode(block, "y", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.draw_image(${image}, ${x}, ${y})\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_load_pbm"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_load_pbm",
            message0: "%{BKY_BEE_OLED_LOAD_BMP}", //%2 load bmp %1
            args0: [
                {
                    type: "input_value",
                    name: "image",
                    check: "String",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_load_pbm"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var image = pythonGenerator.valueToCode(block, "image", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.load_pbm(${image})\n`;
    return code;
};

Blockly.Blocks["beeboard_oled_draw_icon"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_oled_draw_icon",
            message0: "%{BKY_BEE_OLED_DRAW_ICON}", //%2 load jpg %1
            args0: [
                {
                    type: "input_value",
                    name: "image",
                    check: "String",
                },
                {
                    type: "input_value",
                    name: "x",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "y",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: OLedIcon,
                    width: 45,
                    height: 45,
                    alt: "oled",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_oled_draw_icon"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var image = pythonGenerator.valueToCode(block, "image", pythonGenerator.ORDER_ATOMIC);
    var x = pythonGenerator.valueToCode(block, "x", pythonGenerator.ORDER_ATOMIC);
    var y = pythonGenerator.valueToCode(block, "y", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.oled.draw_icon(${image}, ${x}, ${y})\n`;
    return code;
};

// // Define onboard oled
// Blockly.Blocks["beeboard_render_oled"] = {
//     init: function () {
//         this.jsonInit({
//             type: "render_oled",
//             message0: "%{BKY_BEE_OLED_RENDER}",
//             args0: [
//                 {
//                     type: "field_image",
//                     src: OLedIcon,
//                     width: 45,
//                     height: 45,
//                     alt: "oled",
//                 },
//             ],
//             inputsInline: true,
//             previousStatement: null,
//             nextStatement: null,
//             colour: "#3d87ff",
//             tooltip: "",
//             helpUrl: "",
//         });
//     },
// };

// pythonGenerator.forBlock["beeboard_render_oled"] = function (block) {
//     pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
//     var code = `bee.oled.show()\n`;
//     return code;
// };

// Define buzzer note
Blockly.Blocks["beeboard_buzzer_notes"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_buzzer_notes",
            message0: "%{BKY_BEE_BUZZER_NOTES}", //%3 buzzer notes %1 duration %2
            args0: [
                {
                    type: "input_value",
                    name: "notes",
                    check: "String",
                },
                {
                    type: "field_dropdown",
                    name: "duration",
                    color: "white",
                    options: [
                        [
                            {
                                src: WholeNoteIcon,
                                width: 20,
                                height: 20,
                                alt: "whole note",
                            },
                            "4",
                        ],
                        [
                            {
                                src: HalfNoteIcon,
                                width: 20,
                                height: 20,
                                alt: "half note",
                            },
                            "2",
                        ],
                        [
                            {
                                src: QuarterNoteIcon,
                                width: 20,
                                height: 20,
                                alt: "quarter note",
                            },
                            "1",
                        ],
                        [
                            {
                                src: EighthNoteIcon,
                                width: 20,
                                height: 20,
                                alt: "eighth note",
                            },
                            "1 / 2",
                        ],
                        [
                            {
                                src: SixteenthNoteIcon,
                                "background-color": "white",
                                width: 20,
                                height: 20,
                                alt: "sixteenth note",
                            },
                            "1 / 4",
                        ],
                    ],
                },
                {
                    type: "field_image",
                    src: BuzzerIcon,
                    width: 45,
                    height: 45,
                    alt: "buzzer",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff", //"#2C3E50",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_buzzer_notes"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var value_notes = pythonGenerator.valueToCode(block, "notes", pythonGenerator.ORDER_ATOMIC);
    var dropdown_duration = block.getFieldValue("duration");
    var code = `bee.buzzer.play_song(${value_notes}, ${dropdown_duration})\n`;
    return code;
};

// Define buzzer note
Blockly.Blocks["beeboard_make_note"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_make_note",
            message0: "%1 %2 %3",
            args0: [
                {
                    type: "field_image",
                    src: MakeNoteIcon,
                    width: 15,
                    height: 15,
                    alt: "notes",
                    flipRtl: false,
                },
                {
                    type: "field_label_serializable",
                    name: "label",
                    text: "",
                },
                {
                    type: "field_note",
                    name: "notes",
                    value: "",
                },
            ],
            inputsInline: true,
            output: "String",
            colour: "#2C3E50",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_make_note"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var text_notes = block.getFieldValue("notes");
    var code = `'${text_notes}'`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Define buzzer note
Blockly.Blocks["beeboard_buzzer_note"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_buzzer_note",
            message0: "%{BKY_BEE_BUZZER_NOTE}", //%2 play note %1
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "note",
                    columns: 7,
                    options: [
                        ["C4", "C4"],
                        ["D4", "D4"],
                        ["E4", "E4"],
                        ["F4", "F4"],
                        ["G4", "G4"],
                        ["A4", "A4"],
                        ["B4", "B4"],
                        ["C5", "C5"],
                        ["D5", "D5"],
                        ["E5", "E5"],
                        ["F5", "F5"],
                        ["G5", "G5"],
                        ["A5", "A5"],
                        ["B5", "B5"],
                        ["C6", "C6"],
                        ["D6", "D6"],
                        ["E6", "E6"],
                        ["F6", "F6"],
                        ["G6", "G6"],
                        ["A6", "A6"],
                        ["B6", "B6"],
                        ["P", "P"],
                    ],
                },
                {
                    type: "field_image",
                    src: BuzzerIcon,
                    width: 45,
                    height: 45,
                    alt: "buzzer",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_buzzer_note"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var note = block.getFieldValue("note");
    var code = `bee.buzzer.play_tone(bee.buzzer.TONES['${note}'])\n`;
    return code;
};

// Define buzzer note volume
Blockly.Blocks["beeboard_buzzer_volume"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_buzzer_volume",
            message0: "%{BKY_BEE_BUZZER_VOLUME}", //%2 set volume %1 %
            args0: [
                {
                    type: "input_value",
                    name: "volume",
                },
                {
                    type: "field_image",
                    src: BuzzerIcon,
                    width: 45,
                    height: 45,
                    alt: "buzzer",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_buzzer_volume"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var volume = pythonGenerator.valueToCode(block, "volume", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.buzzer.set_volume(${volume})\n`;
    return code;
};

// Define buzzer note quite
Blockly.Blocks["beeboard_be_quiet"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_be_quiet",
            message0: "%{BKY_BEE_BUZZER_QUIET}", //%1 silent
            args0: [
                {
                    type: "field_image",
                    src: BuzzerIcon,
                    width: 45,
                    height: 45,
                    alt: "buzzer",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_be_quiet"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.buzzer.be_quiet()\n`;
    return code;
};

// Define digital write
Blockly.Blocks["beeboard_gpio_output"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_gpio_output",
            message0: "%{BKY_BEE_GPIO_OUTPUT}", //%3 %2 digital write %1
            args0: [
                {
                    type: "field_dropdown",
                    name: "signal",
                    options: [
                        ["%{BKY_BEE_HIGH}", "HIGH"],
                        ["%{BKY_BEE_LOW}", "LOW"],
                    ],
                },
                {
                    type: "field_grid_dropdown",
                    name: "port",
                    columns: 2,
                    options: [
                        ["", ""],
                        ["PORT2", "PORT2"],
                        ["PORT3", "PORT3"],
                        ["PORT4", "PORT4"],
                        ["PORT5", "PORT5"],
                        ["PORT6", "PORT6"],
                    ],
                },
                {
                    type: "field_image",
                    src: GPIOOutputIcon,
                    width: 45,
                    height: 45,
                    alt: "gpio_output",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_gpio_output"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var port = block.getFieldValue("port");
    var signal = block.getFieldValue("signal");
    var code = `bee.digital_write(bee.${port}, bee.${signal})\n`;
    return code;
};

// Define pwm write
Blockly.Blocks["beeboard_pwm_frequency"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_pwm_frequency",
            message0: "%{BKY_BEE_PWM_OUTPUT}", //%3 %2 pwm write %1 %
            args0: [
                {
                    type: "input_value",
                    name: "duty",
                },
                {
                    type: "field_grid_dropdown",
                    name: "port",
                    columns: 2,
                    options: [
                        ["", ""],
                        ["PORT2", "PORT2"],
                        ["PORT3", "PORT3"],
                        ["PORT4", "PORT4"],
                        ["PORT5", "PORT5"],
                        ["PORT6", "PORT6"],
                    ],
                },
                {
                    type: "field_image",
                    src: GPIOOutputIcon,
                    width: 45,
                    height: 45,
                    alt: "pwm_frequency",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#3d87ff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_pwm_frequency"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var port = block.getFieldValue("port");
    var duty = pythonGenerator.valueToCode(block, "duty", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.pwm_write(bee.${port}, ${duty})\n`;
    return code;
};

// Define dc motor
Blockly.Blocks["beeboard_run_dc_motor_onboard"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_run_dc_motor_onboard",
            message0: "%{BKY_BEE_DC_MOTOR}", //%3 %1 set speed to %2% power
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "motor",
                    columns: 2,
                    options: [
                        ["M1", "M1"],
                        ["M2", "M2"],
                    ],
                },
                {
                    type: "input_value",
                    name: "power",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: WheelIcon,
                    width: 45,
                    height: 45,
                    alt: "motor",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#ffb330",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_run_dc_motor_onboard"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var motor = block.getFieldValue("motor");
    var power = pythonGenerator.valueToCode(block, "power", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.dcmotor.speed(bee.${motor}, ${power})\n`;
    return code;
};

// Define break motor
Blockly.Blocks["beeboard_stop_dc_motor"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_stop_dc_motor",
            message0: "%{BKY_BEE_STOP_MOTOR}", //%2 %1 stop motor
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "motor",
                    columns: 2,
                    options: [
                        ["M1", "M1"],
                        ["M2", "M2"],
                    ],
                },
                {
                    type: "field_image",
                    src: WheelIcon,
                    width: 45,
                    height: 45,
                    alt: "motor",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#ffb330",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_stop_dc_motor"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var motor = block.getFieldValue("motor");
    // var power = pythonGenerator.valueToCode(block, 'power', pythonGenerator.ORDER_ATOMIC);
    var code = `bee.dcmotor.brake(bee.${motor})\n`;
    return code;
};

// Define rc servo
Blockly.Blocks["beeboard_run_servo_onboard"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_run_servo_onboard",
            message0: "%{BKY_BEE_RUN_SERVO}", //%3 %1 move %2 degrees
            args0: [
                {
                    type: "field_grid_dropdown",
                    name: "servo",
                    columns: 4,
                    options: [
                        ["S1", "S1"],
                        ["S2", "S2"],
                        ["S3", "S3"],
                        ["S4", "S4"],
                    ],
                },
                {
                    type: "input_value",
                    name: "angle",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: ServoIcon,
                    width: 45,
                    height: 45,
                    alt: "rc servo",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#ffb330",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_run_servo_onboard"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var servo = block.getFieldValue("servo");
    var angle = pythonGenerator.valueToCode(block, "angle", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.servo.position(bee.${servo}, ${angle})\n`;
    return code;
};

// Define robot forward
Blockly.Blocks["beeboard_move_robot_forward"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_move_robot_forward",
            message0: "%{BKY_BEE_MOVE_FORWARD}", //%3 move forward with %1% power in %2 seconds
            args0: [
                {
                    type: "input_value",
                    name: "power",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "sec",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: MovementIcon,
                    width: 45,
                    height: 45,
                    alt: "robot",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#00bfff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_move_robot_forward"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var power = pythonGenerator.valueToCode(block, "power", pythonGenerator.ORDER_ATOMIC);
    var sec = pythonGenerator.valueToCode(block, "sec", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.move_forward(${power}, ${sec})\n`;
    return code;
};

// Define robot backward
Blockly.Blocks["beeboard_move_robot_backward"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_move_robot_backward",
            message0: "%{BKY_BEE_MOVE_BACKWARD}", //%3 move backward with %1% power in %2 seconds
            args0: [
                {
                    type: "input_value",
                    name: "power",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "sec",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: MovementIcon,
                    width: 45,
                    height: 45,
                    alt: "robot",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#00bfff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_move_robot_backward"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var power = pythonGenerator.valueToCode(block, "power", pythonGenerator.ORDER_ATOMIC);
    var sec = pythonGenerator.valueToCode(block, "sec", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.move_backward(${power}, ${sec})\n`;
    return code;
};

// Define robot left
Blockly.Blocks["beeboard_turn_robot_left"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_turn_robot_left",
            message0: "%{BKY_BEE_TURN_LEFT}", //%3 turn left with %1% power in %2 seconds
            args0: [
                {
                    type: "input_value",
                    name: "power",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "sec",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: MovementIcon,
                    width: 45,
                    height: 45,
                    alt: "robot",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#00bfff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_turn_robot_left"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var power = pythonGenerator.valueToCode(block, "power", pythonGenerator.ORDER_ATOMIC);
    var sec = pythonGenerator.valueToCode(block, "sec", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.turn_left(${power}, ${sec})\n`;
    return code;
};

// Define robot right
Blockly.Blocks["beeboard_turn_robot_right"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_turn_robot_right",
            message0: "%{BKY_BEE_TURN_RIGHT}", //%3 turn right with %1% power in %2 seconds
            args0: [
                {
                    type: "input_value",
                    name: "power",
                    check: "Number",
                },
                {
                    type: "input_value",
                    name: "sec",
                    check: "Number",
                },
                {
                    type: "field_image",
                    src: MovementIcon,
                    width: 45,
                    height: 45,
                    alt: "robot",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#00bfff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_turn_robot_right"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var power = pythonGenerator.valueToCode(block, "power", pythonGenerator.ORDER_ATOMIC);
    var sec = pythonGenerator.valueToCode(block, "sec", pythonGenerator.ORDER_ATOMIC);
    var code = `bee.turn_right(${power}, ${sec})\n`;
    return code;
};

// Define robot stop
Blockly.Blocks["beeboard_stop_robot"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_stop_robot",
            message0: "%{BKY_BEE_STOP_ROBOT}", //%1 stop robot
            args0: [
                {
                    type: "field_image",
                    src: MovementIcon,
                    width: 45,
                    height: 45,
                    alt: "robot",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#00bfff",
            tooltip: "",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_stop_robot"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.stop_robot()\n`;
    return code;
};

// Define the hat block for OTA update
Blockly.Blocks["beeboard_setup_ota"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_setup_ota",
            message0: "%{BKY_BEE_SET_OTA}", //%3 set OTA with Wifi %1 Password %2
            args0: [
                {
                    type: "field_input",
                    name: "wifi_ssid",
                    text: "my_wifi",
                },
                {
                    type: "field_input",
                    name: "wifi_pass",
                    text: "my_pass",
                },
                {
                    type: "field_image",
                    src: OTAIcon,
                    width: 45,
                    height: 45,
                    alt: "ota",
                },
            ],
            previousStatement: null,
            colour: "#FFD500",
            tooltip: "Setup OTA through Wifi",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_setup_ota"] = function (block) {
    var wifi_ssid = block.getFieldValue("wifi_ssid");
    var wifi_pass = block.getFieldValue("wifi_pass");
    var code = `
# == OTA ==
import network
import socket
import machine
import time
import _thread
from BeeBrain import bee

SSID = "${wifi_ssid}"
PASSWORD = "${wifi_pass}"
reload_main = True

def connect_wifi():
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)
    wlan.connect(SSID, PASSWORD)
    bee.oled.clear()
    bee.oled.text("Connecting to", 0, 0, 1)
    bee.oled.text(SSID, 0, 12, 1)
    bee.oled.show()
    for _ in range(10):
        if wlan.isconnected():
            ip = wlan.ifconfig()[0]
            return True, ip
        time.sleep(1)
    return False, _

def start_server(ip):
    global reload_main
    addr = socket.getaddrinfo("0.0.0.0", 80)[0][-1]
    server = socket.socket()
    # Release port
    try:
        server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server.bind(addr)
        server.listen(1)
        bee.oled.clear()
        bee.oled.text("Server IP:", 0, 0, 1)
        bee.oled.text(ip, 0, 12, 1)
        bee.oled.show()
    except OSError as e:
        bee.oled.clear()
        bee.oled.text("Server error", 0, 0, 1)
        bee.oled.show()
        server.close()
        return
    
    while True:
        conn, addr = server.accept()
        request = conn.recv(1024).decode()

        if "POST" in request:
            # Parse request to get filename from URL
            request_lines = request.split('\\r\\n')
            first_line = request_lines[0]
            url_path = first_line.split(' ')[1]

            # Extract filename from URL path
            filename = url_path.split('/')[-1] if '/' in url_path else 'main.py'

            bee.oled.clear()
            bee.oled.text("Server IP:", 0, 0, 1)
            bee.oled.text(ip, 0, 12, 1)
            bee.oled.text(f"Recv: {filename}", 0, 24, 1)
            bee.oled.show()

            code = request.split("%@%@")[1]
            with open(filename, "w") as f:
                f.write(code)

            if filename == "main.py":
                reload_main = True
                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
                time.sleep(0.5)
                bee.neopixel.set_rgb(bee.LED1, 0, 0, 0)

            # Header CORS
            cors_headers = (
                b"HTTP/1.1 200 OK%@"
                b"Access-Control-Allow-Origin: *%@"
                b"Content-Type: text/plain%@%@"
            )
            conn.send(cors_headers + b"Upload successful")
        else:
            # Header CORS
            cors_headers = (
                b"HTTP/1.1 200 OK%@"
                b"Access-Control-Allow-Origin: *%@"
                b"Content-Type: text/plain%@%@"
            )
            conn.send(cors_headers + b"OTA Ready")
        conn.close()

is_connected, ip = connect_wifi()
if is_connected:
    _thread.start_new_thread(start_server, (ip,))
    while True:
        if reload_main:
            bee.neopixel.set_rgb(bee.LED2, 0, 255, 0)
            time.sleep(0.5)
            bee.neopixel.set_rgb(bee.LED2, 0, 0, 0)
            bee.oled.clear()
            bee.oled.text("Server IP:", 0, 0, 1)
            bee.oled.text(ip, 0, 12, 1)
            bee.oled.text("Reloading main...", 0, 24, 1)
            bee.oled.show()
            reload_main = False
            with open("main.py", "r") as f:
                exec(f.read())
else:
    bee.oled.clear()
    bee.oled.text("Can't connect to", 0, 0, 1)
    bee.oled.text(SSID, 0, 12, 1)
    bee.oled.show()
`;
    return code;
};

Blockly.Blocks["beeboard_setup_serial"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_setup_serial",
            message0: "%{BKY_BEE_SET_SERIAL}", //%1 set serial
            args0: [
                {
                    type: "field_image",
                    src: UsbIcon,
                    width: 45,
                    height: 45,
                    alt: "usb",
                },
            ],
            previousStatement: null,
            colour: "#FFD500",
            tooltip: "Setup Serial",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_setup_serial"] = function (block) {
    var code = "# == Serial ==";
    return code;
};

Blockly.Blocks["beeboard_setup_bluetooth"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_setup_bluetooth",
            message0: "%{BKY_BEE_SET_BLUETOOTH}", //%1 set bluetooth
            args0: [
                {
                    type: "field_image",
                    src: BluetoothIcon,
                    width: 45,
                    height: 45,
                    alt: "ble",
                },
            ],
            previousStatement: null,
            colour: "#FFD500",
            tooltip: "Setup BLE",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_setup_bluetooth"] = function (block) {
    var code = "import BeeBLESupervisor\n";
    return code;
};

Blockly.Blocks["beeboard_ota_ip"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_ota_ip",
            message0: "%{BKY_BEE_OTA_IP}", //%2 set OTA IP %1
            args0: [
                {
                    type: "field_input",
                    name: "ip_address",
                    text: "***********",
                },
                {
                    type: "field_image",
                    src: OTAIcon,
                    width: 45,
                    height: 45,
                    alt: "ota",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#FFD500",
            tooltip: "Setup OTA through Wifi",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_ota_ip"] = function (block) {
    var ip_address = block.getFieldValue("ip_address");
    var code = `# == OTA IP: ${ip_address}\n`;
    return code;
};

Blockly.Blocks["beeboard_reset"] = {
    init: function () {
        this.jsonInit({
            type: "beeboard_reset",
            message0: "%{BKY_BEE_RESET}", //%1 reset board
            args0: [
                {
                    type: "field_image",
                    src: ResetIcon,
                    width: 45,
                    height: 45,
                    alt: "ota",
                },
            ],
            inputsInline: true,
            previousStatement: null,
            nextStatement: null,
            colour: "#FFD500",
            tooltip: "Reset BeeBoard",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["beeboard_reset"] = function (block) {
    pythonGenerator.definitions_["from_BeeBrain_import_bee"] = "from BeeBrain import bee";
    var code = `bee.init_bee()\n`;
    return code;
};

// Export all blocks and generators
export const BeeBoardBlocks = {
    beeboard_led: Blockly.Blocks["beeboard_led"],
    beeboard_onboard_button_status: Blockly.Blocks["beeboard_onboard_button_status"],
    beeboard_gpio_input: Blockly.Blocks["beeboard_gpio_input"],
    beeboard_analog_input: Blockly.Blocks["beeboard_analog_input"],
    beeboard_gyro_refresh: Blockly.Blocks["beeboard_gyro_refresh"],
    beeboard_gyro_roll: Blockly.Blocks["beeboard_gyro_roll"],
    beeboard_gyro_pitch: Blockly.Blocks["beeboard_gyro_pitch"],
    beeboard_onboard_led_rgb: Blockly.Blocks["beeboard_onboard_led_rgb"],
    beeboard_onboard_led_on_picker: Blockly.Blocks["beeboard_onboard_led_on_picker"],
    beeboard_onboard_led_off: Blockly.Blocks["beeboard_onboard_led_off"],
    beeboard_display_oled: Blockly.Blocks["beeboard_display_oled"],
    beeboard_clear_oled: Blockly.Blocks["beeboard_clear_oled"],
    beeboard_render_oled: Blockly.Blocks["beeboard_render_oled"],
    beeboard_buzzer_notes: Blockly.Blocks["beeboard_buzzer_notes"],
    beeboard_make_note: Blockly.Blocks["beeboard_make_note"],
    beeboard_buzzer_note: Blockly.Blocks["beeboard_buzzer_note"],
    beeboard_buzzer_volume: Blockly.Blocks["beeboard_buzzer_volume"],
    beeboard_gpio_output: Blockly.Blocks["beeboard_gpio_output"],
    beeboard_be_quiet: Blockly.Blocks["beeboard_be_quiet"],
    beeboard_run_dc_motor_onboard: Blockly.Blocks["beeboard_run_dc_motor_onboard"],
    beeboard_stop_dc_motor: Blockly.Blocks["beeboard_stop_dc_motor"],
    beeboard_run_servo_onboard: Blockly.Blocks["beeboard_run_servo_onboard"],
    beeboard_move_robot_forward: Blockly.Blocks["beeboard_move_robot_forward"],
    beeboard_move_robot_backward: Blockly.Blocks["beeboard_move_robot_backward"],
    beeboard_turn_robot_left: Blockly.Blocks["beeboard_turn_robot_left"],
    beeboard_turn_robot_right: Blockly.Blocks["beeboard_turn_robot_right"],
    beeboard_stop_robot: Blockly.Blocks["beeboard_stop_robot"],
    beeboard_pwm_frequency: Blockly.Blocks["beeboard_pwm_frequency"],
    beeboard_onboard_button_interrupt_handler_start_program:
        Blockly.Blocks["beeboard_onboard_button_interrupt_handler_start_program"],
    beeboard_setup_ota: Blockly.Blocks["beeboard_setup_ota"],
    beeboard_setup_serial: Blockly.Blocks["beeboard_setup_serial"],
    beeboard_ota_ip: Blockly.Blocks["beeboard_ota_ip"],
    beeboard_reset: Blockly.Blocks["beeboard_reset"],
    beeboard_gyro_calibrate: Blockly.Blocks["beeboard_gyro_calibrate"],
    beeboard_gyro_rpy: Blockly.Blocks["beeboard_gyro_rpy"],
    beeboard_gyro_skaking: Blockly.Blocks["beeboard_gyro_skaking"],
};

export const BeeBoardGenerators = {
    beeboard_led: pythonGenerator["beeboard_led"],
    beeboard_onboard_button_status: pythonGenerator["beeboard_onboard_button_status"],
    beeboard_gpio_input: pythonGenerator["beeboard_gpio_input"],
    beeboard_analog_input: pythonGenerator["beeboard_analog_input"],
    beeboard_gyro_refresh: pythonGenerator["beeboard_gyro_refresh"],
    beeboard_gyro_roll: pythonGenerator["beeboard_gyro_roll"],
    beeboard_gyro_pitch: pythonGenerator["beeboard_gyro_pitch"],
    beeboard_onboard_led_rgb: pythonGenerator["beeboard_onboard_led_rgb"],
    beeboard_onboard_led_on_picker: pythonGenerator["beeboard_onboard_led_on_picker"],
    beeboard_onboard_led_off: pythonGenerator["beeboard_onboard_led_off"],
    beeboard_display_oled: pythonGenerator["beeboard_display_oled"],
    beeboard_clear_oled: pythonGenerator["beeboard_clear_oled"],
    beeboard_render_oled: pythonGenerator["beeboard_render_oled"],
    beeboard_buzzer_notes: pythonGenerator["beeboard_buzzer_notes"],
    beeboard_make_note: pythonGenerator["beeboard_make_note"],
    beeboard_buzzer_note: pythonGenerator["beeboard_buzzer_note"],
    beeboard_buzzer_volume: pythonGenerator["beeboard_buzzer_volume"],
    beeboard_gpio_output: pythonGenerator["beeboard_gpio_output"],
    beeboard_be_quiet: pythonGenerator["beeboard_be_quiet"],
    beeboard_run_dc_motor_onboard: pythonGenerator["beeboard_run_dc_motor_onboard"],
    beeboard_stop_dc_motor: pythonGenerator["beeboard_stop_dc_motor"],
    beeboard_run_servo_onboard: pythonGenerator["beeboard_run_servo_onboard"],
    beeboard_move_robot_forward: pythonGenerator["beeboard_move_robot_forward"],
    beeboard_move_robot_backward: pythonGenerator["beeboard_move_robot_backward"],
    beeboard_turn_robot_left: pythonGenerator["beeboard_turn_robot_left"],
    beeboard_turn_robot_right: pythonGenerator["beeboard_turn_robot_right"],
    beeboard_stop_robot: pythonGenerator["beeboard_stop_robot"],
    beeboard_pwm_frequency: pythonGenerator["beeboard_pwm_frequency"],
    beeboard_onboard_button_interrupt_handler_start_program:
        pythonGenerator["beeboard_onboard_button_interrupt_handler_start_program"],
    beeboard_setup_ota: pythonGenerator["beeboard_setup_ota"],
    beeboard_setup_serial: pythonGenerator["beeboard_setup_serial"],
    beeboard_ota_ip: pythonGenerator["beeboard_ota_ip"],
    beeboard_reset: pythonGenerator["beeboard_reset"],
    beeboard_gyro_calibrate: pythonGenerator["beeboard_gyro_calibrate"],
    beeboard_gyro_rpy: pythonGenerator["beeboard_gyro_rpy"],
    beeboard_gyro_skaking: pythonGenerator["beeboard_gyro_skaking"],
};
