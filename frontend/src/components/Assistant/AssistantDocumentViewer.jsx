import React, { useState, useEffect } from "react";
import {
    <PERSON>,
    Typo<PERSON>,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Alert,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    <PERSON><PERSON>,
    Divider,
    TextField,
} from "@mui/material";
import {
    Description as DocumentIcon,
    Visibility as ViewIcon,
    Close as CloseIcon,
    Upload as UploadIcon,
    Delete as DeleteIcon,
    Add as AddIcon,
} from "@mui/icons-material";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import { beeColors } from "../Common/CustomButton";
import CustomButton from "../Common/CustomButton";
import CustomTextField from "../Common/CustomTextField";
import axiosInstance from "../../services/axiosInstance";
import { green, grey } from "@mui/material/colors";

const AssistantDocumentViewer = ({ assistantType, assistantName, showNotification }) => {
    const [documents, setDocuments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [viewDialog, setViewDialog] = useState({
        open: false,
        document: null,
        content: "",
        loading: false,
    });
    const [deleteDialog, setDeleteDialog] = useState({
        open: false,
        document: null,
        deleting: false,
    });
    const [noteDialog, setNoteDialog] = useState({
        open: false,
        title: "",
        content: "",
        saving: false,
    });

    // Helper function to check if file is markdown
    const isMarkdownFile = (filename) => {
        return filename?.toLowerCase().endsWith(".md");
    };

    // CodeBlock component for syntax highlighting
    const CodeBlock = ({ node, inline, className, children, ...props }) => {
        const match = /language-(\w+)/.exec(className || "");
        const language = match ? match[1] : null;
        console.log(language);
        if (language === "" || language === null) {
            return (
                <Box
                    component="code"
                    sx={{
                        backgroundColor: "#f8f9fa",
                        padding: "2px 6px",
                        borderRadius: "6px",
                        fontSize: "13px",
                        fontFamily: '"Roboto Mono", "SF Mono", Monaco, monospace',
                        color: "#d73a49",
                        border: "1px solid #e1e4e8",
                    }}
                    {...props}
                >
                    {children}
                </Box>
            );
        }

        return (
            <Box sx={{ position: "relative", mb: 2 }}>
                {/* Language Label */}
                {language && (
                    <Box
                        sx={{
                            position: "absolute",
                            top: 8,
                            left: 12,
                            zIndex: 1,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            color: "rgba(255, 255, 255, 0.7)",
                            padding: "2px 8px",
                            borderRadius: "4px",
                            fontSize: "11px",
                            fontWeight: 500,
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                        }}
                    >
                        {language}
                    </Box>
                )}

                <SyntaxHighlighter
                    style={vscDarkPlus}
                    language={language || "text"}
                    PreTag="div"
                    customStyle={{
                        margin: 0,
                        borderRadius: "8px",
                        fontSize: "13px",
                        lineHeight: 1.45,
                        fontFamily: '"Roboto Mono", "SF Mono", Monaco, monospace',
                        paddingTop: "40px",
                    }}
                    {...props}
                >
                    {String(children).replace(/\n$/, "")}
                </SyntaxHighlighter>
            </Box>
        );
    };

    // Load documents for this assistant
    const loadDocuments = async () => {
        if (!assistantType) return;

        try {
            setLoading(true);
            const response = await axiosInstance.get(`/api/assistant/documents/?assistant_type=${assistantType}`);
            setDocuments(response.data);
        } catch (error) {
            console.error("Error loading documents:", error);
            setDocuments([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadDocuments();
    }, [assistantType]);

    // Handle multiple file upload
    const handleMultipleFileUpload = async (event) => {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        setUploading(true);
        let successCount = 0;
        let errorCount = 0;

        for (const file of files) {
            try {
                const formData = new FormData();
                formData.append("file", file);
                formData.append("assistant_type", assistantType);

                await axiosInstance.post("/api/assistant/upload-assistant/", formData, {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                });
                successCount++;
            } catch (error) {
                console.error(`Upload error for ${file.name}:`, error);
                errorCount++;
            }
        }

        // Show notification based on results
        if (successCount > 0 && errorCount === 0) {
            showNotification && showNotification(`Đã upload thành công ${successCount} tài liệu!`, "success");
        } else if (successCount > 0 && errorCount > 0) {
            showNotification &&
                showNotification(
                    `Upload thành công ${successCount} tài liệu, thất bại ${errorCount} tài liệu`,
                    "warning"
                );
        } else {
            showNotification && showNotification("Lỗi khi upload tài liệu", "error");
        }

        // Reload documents and reset input
        loadDocuments();
        event.target.value = "";
        setUploading(false);
    };

    // Handle delete document
    const handleDeleteDocument = (document) => {
        setDeleteDialog({
            open: true,
            document,
            deleting: false,
        });
    };

    const confirmDeleteDocument = async () => {
        if (!deleteDialog.document) return;

        try {
            setDeleteDialog((prev) => ({ ...prev, deleting: true }));

            await axiosInstance.delete(`/api/assistant/documents/${deleteDialog.document.id}/`);

            showNotification && showNotification("Đã xóa tài liệu thành công!", "success");
            loadDocuments(); // Reload documents
            setDeleteDialog({ open: false, document: null, deleting: false });
        } catch (error) {
            console.error("Delete document error:", error);
            showNotification && showNotification("Lỗi khi xóa tài liệu", "error");
            setDeleteDialog((prev) => ({ ...prev, deleting: false }));
        }
    };

    const cancelDeleteDocument = () => {
        setDeleteDialog({ open: false, document: null, deleting: false });
    };

    // Handle note dialog
    const handleOpenNoteDialog = () => {
        setNoteDialog({
            open: true,
            title: "",
            content:
                "# Ghi chú mới\n\nViết nội dung ghi chú của bạn ở đây...\n\n## Ví dụ\n\n- Điểm 1\n- Điểm 2\n- Điểm 3\n\n```javascript\n// Code example\nconsole.log('Hello World!');\n```",
            saving: false,
        });
    };

    const handleCloseNoteDialog = () => {
        setNoteDialog({
            open: false,
            title: "",
            content: "",
            saving: false,
        });
    };

    const handleSaveNote = async () => {
        if (!noteDialog.title.trim()) {
            showNotification && showNotification("Vui lòng nhập tiêu đề cho ghi chú", "warning");
            return;
        }

        try {
            setNoteDialog((prev) => ({ ...prev, saving: true }));

            // Create a blob with markdown content
            const blob = new Blob([noteDialog.content], { type: "text/markdown" });
            const file = new File([blob], `${noteDialog.title}.md`, { type: "text/markdown" });

            const formData = new FormData();
            formData.append("file", file);
            formData.append("assistant_type", assistantType);

            await axiosInstance.post("/api/assistant/upload-assistant/", formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });

            showNotification && showNotification("Đã lưu ghi chú thành công!", "success");
            loadDocuments(); // Reload documents
            handleCloseNoteDialog();
        } catch (error) {
            console.error("Save note error:", error);
            showNotification && showNotification("Lỗi khi lưu ghi chú", "error");
            setNoteDialog((prev) => ({ ...prev, saving: false }));
        }
    };

    // Handle view document
    const handleViewDocument = async (document) => {
        try {
            setViewDialog({ open: true, document, content: "", loading: true });

            const response = await axiosInstance.get(`/api/assistant/documents/${document.id}/content/`);

            setViewDialog((prev) => ({
                ...prev,
                content: response.data.content || "Không thể đọc nội dung file này.",
                loading: false,
            }));
        } catch (error) {
            setViewDialog((prev) => ({
                ...prev,
                content: "Lỗi khi tải nội dung file. Vui lòng thử lại sau.",
                loading: false,
            }));
            console.error("View document error:", error);
        }
    };

    const handleCloseViewDialog = () => {
        setViewDialog({ open: false, document: null, content: "", loading: false });
    };

    // Render document content based on file type
    const renderDocumentContent = (document, content) => {
        if (isMarkdownFile(document?.title || "")) {
            return (
                <Box
                    sx={{
                        fontFamily: '"Google Sans", "Roboto", "Helvetica Neue", Arial, sans-serif',
                        fontSize: "14px",
                        lineHeight: 1.6,
                        letterSpacing: "0.2px",
                        color: "#3c4043",
                        "& p": {
                            margin: "0 0 12px 0",
                            lineHeight: 1.65,
                            fontSize: "14px",
                            "&:last-child": {
                                marginBottom: 0,
                            },
                        },
                        "& h1, & h2, & h3, & h4, & h5, & h6": {
                            margin: "20px 0 12px 0",
                            fontWeight: 500,
                            color: "#202124",
                            letterSpacing: "-0.2px",
                            "&:first-child": {
                                marginTop: 0,
                            },
                        },
                        "& h1": { fontSize: "24px" },
                        "& h2": { fontSize: "20px" },
                        "& h3": { fontSize: "18px" },
                        "& h4": { fontSize: "16px" },
                        "& h5": { fontSize: "14px" },
                        "& h6": { fontSize: "13px" },
                        "& ul, & ol": {
                            margin: "8px 0 16px 0",
                            paddingLeft: "24px",
                        },
                        "& li": {
                            margin: "4px 0",
                            lineHeight: 1.6,
                        },
                        "& blockquote": {
                            margin: "16px 0",
                            padding: "12px 16px",
                            borderLeft: `4px solid ${beeColors.primary.main}`,
                            backgroundColor: "#f8f9fa",
                            fontStyle: "italic",
                            color: "#5f6368",
                        },
                        "& table": {
                            width: "100%",
                            borderCollapse: "collapse",
                            margin: "16px 0",
                            fontSize: "13px",
                        },
                        "& th, & td": {
                            border: "1px solid #dadce0",
                            padding: "8px 12px",
                            textAlign: "left",
                        },
                        "& th": {
                            backgroundColor: "#f8f9fa",
                            fontWeight: 500,
                            color: "#202124",
                        },
                        "& td": {
                            color: "#3c4043",
                        },
                        "& a": {
                            color: beeColors.primary.main,
                            textDecoration: "none",
                            "&:hover": {
                                textDecoration: "underline",
                            },
                        },
                        "& strong": {
                            fontWeight: 500,
                            color: "#202124",
                        },
                        "& em": {
                            fontStyle: "italic",
                            color: "#5f6368",
                        },
                    }}
                >
                    <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                            code: CodeBlock,
                        }}
                    >
                        {content}
                    </ReactMarkdown>
                </Box>
            );
        } else {
            // Plain text rendering for non-markdown files
            return (
                <Typography
                    component="pre"
                    sx={{
                        whiteSpace: "pre-wrap",
                        fontFamily: "monospace",
                        fontSize: "0.875rem",
                        lineHeight: 1.5,
                    }}
                >
                    {content}
                </Typography>
            );
        }
    };

    const getFileTypeColor = (fileType) => {
        switch (fileType) {
            case "pdf":
                return "error";
            case "txt":
                return "primary";
            case "md":
                return "secondary";
            default:
                return "default";
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    if (loading) {
        return (
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                <CircularProgress size={24} />
            </Box>
        );
    }

    return (
        <Box>
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold" sx={{ color: beeColors.neutral.main }}>
                    Tài Liệu
                </Typography>
                <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                        variant="outlined"
                        onClick={handleOpenNoteDialog}
                        sx={{
                            fontSize: "0.75rem",
                            minWidth: "auto",
                            px: 1.5,
                            height: "40px",
                            borderRadius: "12px",
                            border: `2px solid`,
                            textTransform: "none",
                        }}
                        startIcon={<AddIcon />}
                    >
                        Thêm Note
                    </Button>
                    <CustomButton
                        variant="outlined"
                        component="label"
                        size="small"
                        startIcon={uploading ? <CircularProgress size={16} color="inherit" /> : <UploadIcon />}
                        disabled={uploading}
                        sx={{
                            fontSize: "0.75rem",
                            minWidth: "auto",
                            px: 1.5,
                            height: "40px",
                        }}
                    >
                        {uploading ? "Đang upload..." : "Thêm tài liệu"}
                        <input
                            type="file"
                            hidden
                            multiple
                            accept=".txt,.md,.pdf"
                            onChange={handleMultipleFileUpload}
                            disabled={uploading}
                        />
                    </CustomButton>
                </Box>
            </Box>

            {documents.length === 0 ? (
                <Alert severity="info" sx={{ borderRadius: "8px" }}>
                    Chưa có tài liệu nào cho {assistantName}. Bạn có thể thêm tài liệu trong System Panel.
                </Alert>
            ) : (
                <Box sx={{ maxHeight: "300px", overflowY: "auto" }}>
                    {documents.map((doc, index) => (
                        <Card
                            key={doc.id}
                            variant="outlined"
                            sx={{
                                mb: 1,
                                borderRadius: "8px",
                                transition: "all 0.2s ease",
                                "&:hover": {
                                    boxShadow: 2,
                                    borderColor: beeColors.primary.main,
                                },
                            }}
                        >
                            <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                                <Box
                                    sx={{ display: "flex", alignItems: "flex-start", justifyContent: "space-between" }}
                                >
                                    <Box sx={{ flex: 1, mr: 1 }}>
                                        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                                            <DocumentIcon sx={{ mr: 1, fontSize: 16, color: beeColors.primary.main }} />
                                            <Typography
                                                variant="body2"
                                                fontWeight="bold"
                                                sx={{
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                    flex: 1,
                                                }}
                                            >
                                                {doc.title}
                                            </Typography>
                                        </Box>
                                        <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap" }}>
                                            <Chip
                                                label={doc.file_type?.toUpperCase()}
                                                size="small"
                                                color={getFileTypeColor(doc.file_type)}
                                                variant="outlined"
                                            />
                                            <Typography variant="caption" color="text.secondary">
                                                {formatFileSize(doc.file_size)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                    <Box sx={{ display: "flex", gap: 0.5 }}>
                                        <Tooltip title="Xem nội dung">
                                            <IconButton
                                                // size="small"
                                                onClick={() => handleViewDocument(doc)}
                                                sx={{
                                                    color: green[500],
                                                    "&:hover": {
                                                        backgroundColor: `${beeColors.primary.main}10`,
                                                    },
                                                }}
                                            >
                                                <ViewIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Xóa tài liệu">
                                            <IconButton
                                                onClick={() => handleDeleteDocument(doc)}
                                                sx={{
                                                    color: grey[500],
                                                    "&:hover": {
                                                        color: "#d32f2f",
                                                        backgroundColor: "#ffebee",
                                                    },
                                                }}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    ))}
                </Box>
            )}

            {/* View Document Dialog */}
            <Dialog
                open={viewDialog.open}
                onClose={handleCloseViewDialog}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: "16px",
                        maxHeight: "80vh",
                    },
                }}
            >
                <DialogTitle
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        pb: 1,
                        borderBottom: `1px solid ${beeColors.neutral.main}20`,
                    }}
                >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <DocumentIcon sx={{ mr: 1, color: beeColors.primary.main }} />
                        <Typography variant="h6" fontWeight="bold">
                            {viewDialog.document?.title}
                        </Typography>
                    </Box>
                    <IconButton onClick={handleCloseViewDialog} size="small">
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    {viewDialog.loading ? (
                        <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <Box
                            sx={{
                                maxHeight: "400px",
                                overflowY: "auto",
                                p: 1,
                            }}
                        >
                            {renderDocumentContent(viewDialog.document, viewDialog.content)}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions sx={{ p: 2, pt: 1 }}>
                    <CustomButton onClick={handleCloseViewDialog} variant="outlined" sx={{ height: "40px" }}>
                        Đóng
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog
                open={deleteDialog.open}
                onClose={cancelDeleteDocument}
                maxWidth="sm"
                fullWidth
                sx={{
                    "& .MuiDialog-paper": {
                        borderRadius: "16px",
                    },
                }}
            >
                <DialogTitle>
                    <Typography variant="h6" fontWeight="bold" color="error">
                        Xác nhận xóa tài liệu
                    </Typography>
                </DialogTitle>
                <DialogContent>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                        Bạn có chắc chắn muốn xóa tài liệu này không?
                    </Typography>
                    <Typography variant="body2" fontWeight="bold" color="text.secondary">
                        {deleteDialog.document?.title}
                    </Typography>
                    <Alert severity="warning" sx={{ mt: 2, borderRadius: "8px" }}>
                        Hành động này không thể hoàn tác!
                    </Alert>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button
                        onClick={cancelDeleteDocument}
                        variant="outlined"
                        color="inherit"
                        disabled={deleteDialog.deleting}
                        sx={{ borderRadius: "12px", height: "40px", marginRight: "10px", textTransform: "none" }}
                    >
                        Hủy
                    </Button>
                    <Button
                        onClick={confirmDeleteDocument}
                        variant="contained"
                        color="error"
                        disabled={deleteDialog.deleting}
                        startIcon={
                            deleteDialog.deleting ? <CircularProgress size={16} color="inherit" /> : <DeleteIcon />
                        }
                        sx={{ borderRadius: "12px", height: "40px", textTransform: "none" }}
                    >
                        {deleteDialog.deleting ? "Đang xóa..." : "Xóa"}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Add Note Dialog */}
            <Dialog
                open={noteDialog.open}
                onClose={handleCloseNoteDialog}
                maxWidth="lg"
                fullWidth
                sx={{
                    "& .MuiDialog-paper": {
                        borderRadius: "16px",
                        height: "80vh",
                        maxHeight: "80vh",
                    },
                }}
            >
                <DialogTitle>
                    <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                        <Typography variant="h6" fontWeight="bold">
                            Thêm Ghi Chú Mới
                        </Typography>
                        <IconButton onClick={handleCloseNoteDialog} size="small">
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <DialogContent sx={{ p: 0, display: "flex", flexDirection: "column", height: "100%" }}>
                    {/* Title Input */}
                    <Box sx={{ p: 3, pb: 2 }}>
                        <CustomTextField
                            fullWidth
                            label="Tiêu đề ghi chú"
                            value={noteDialog.title}
                            onChange={(e) => setNoteDialog((prev) => ({ ...prev, title: e.target.value }))}
                            variant="outlined"
                            sx={{ mb: 2 }}
                        />
                    </Box>

                    {/* Split Panel: Editor and Preview */}
                    <Box sx={{ display: "flex", flex: 1, minHeight: 0 }}>
                        {/* Left Panel - Editor */}
                        <Box
                            sx={{ flex: 1, borderRight: "1px solid #e0e0e0", display: "flex", flexDirection: "column" }}
                        >
                            <Box sx={{ p: 2, pb: 1, borderBottom: "1px solid #e0e0e0" }}>
                                <Typography variant="subtitle2" fontWeight="bold" color="text.secondary">
                                    Markdown Editor
                                </Typography>
                            </Box>
                            <Box sx={{ flex: 1, p: 2 }}>
                                <CustomTextField
                                    fullWidth
                                    multiline
                                    value={noteDialog.content}
                                    onChange={(e) => setNoteDialog((prev) => ({ ...prev, content: e.target.value }))}
                                    variant="outlined"
                                    placeholder="Viết nội dung markdown của bạn ở đây..."
                                    sx={{
                                        height: "100%",
                                        "& .MuiInputBase-root": {
                                            height: "100%",
                                            alignItems: "flex-start",
                                        },
                                        "& .MuiInputBase-input": {
                                            height: "100% !important",
                                            overflow: "auto !important",
                                            fontFamily: '"Roboto Mono", monospace',
                                            fontSize: "14px",
                                            lineHeight: 1.5,
                                        },
                                    }}
                                />
                            </Box>
                        </Box>

                        {/* Right Panel - Preview */}
                        <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
                            <Box sx={{ p: 2, pb: 1, borderBottom: "1px solid #e0e0e0" }}>
                                <Typography variant="subtitle2" fontWeight="bold" color="text.secondary">
                                    Preview
                                </Typography>
                            </Box>
                            <Box
                                sx={{
                                    flex: 1,
                                    p: 2,
                                    overflow: "auto",
                                    backgroundColor: "#fafafa",
                                }}
                            >
                                {renderDocumentContent({ title: `${noteDialog.title}.md` }, noteDialog.content)}
                            </Box>
                        </Box>
                    </Box>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button
                        onClick={handleCloseNoteDialog}
                        variant="outlined"
                        color="inherit"
                        disabled={noteDialog.saving}
                        sx={{ borderRadius: "12px", marginRight: "10px", height: "100%", textTransform: "none" }}
                    >
                        Hủy
                    </Button>
                    <CustomButton
                        onClick={handleSaveNote}
                        variant="contained"
                        disabled={noteDialog.saving || !noteDialog.title.trim()}
                        startIcon={noteDialog.saving ? <CircularProgress size={16} color="inherit" /> : <AddIcon />}
                        sx={{ textTransform: "none" }}
                    >
                        {noteDialog.saving ? "Đang lưu..." : "Lưu Ghi Chú"}
                    </CustomButton>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default AssistantDocumentViewer;
